-- Merging decision tree log ---
manifest
ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\src\main\AndroidManifest.xml:1:1-20:12
INJECTED from C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\src\main\AndroidManifest.xml:1:1-20:12
	package
		INJECTED from C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\src\main\AndroidManifest.xml:1:11-69
application
ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\src\main\AndroidManifest.xml:3:5-16:19
receiver#com.capacitorjs.plugins.localnotifications.TimedNotificationPublisher
ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\src\main\AndroidManifest.xml:4:9-106
	android:name
		ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\src\main\AndroidManifest.xml:4:19-103
receiver#com.capacitorjs.plugins.localnotifications.NotificationDismissReceiver
ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\src\main\AndroidManifest.xml:5:9-107
	android:name
		ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\src\main\AndroidManifest.xml:5:19-104
receiver#com.capacitorjs.plugins.localnotifications.LocalNotificationRestoreReceiver
ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\src\main\AndroidManifest.xml:6:9-15:20
	android:exported
		ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\src\main\AndroidManifest.xml:9:13-37
	android:directBootAware
		ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\src\main\AndroidManifest.xml:8:13-43
	android:name
		ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\src\main\AndroidManifest.xml:7:13-103
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.LOCKED_BOOT_COMPLETED+action:name:android.intent.action.QUICKBOOT_POWERON
ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\src\main\AndroidManifest.xml:10:13-14:29
action#android.intent.action.LOCKED_BOOT_COMPLETED
ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\src\main\AndroidManifest.xml:11:17-86
	android:name
		ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\src\main\AndroidManifest.xml:11:25-83
action#android.intent.action.BOOT_COMPLETED
ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\src\main\AndroidManifest.xml:12:17-79
	android:name
		ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\src\main\AndroidManifest.xml:12:25-76
action#android.intent.action.QUICKBOOT_POWERON
ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\src\main\AndroidManifest.xml:13:17-82
	android:name
		ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\src\main\AndroidManifest.xml:13:25-79
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\src\main\AndroidManifest.xml:17:5-80
	android:name
		ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\src\main\AndroidManifest.xml:17:22-78
uses-permission#android.permission.WAKE_LOCK
ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\src\main\AndroidManifest.xml:18:5-67
	android:name
		ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\src\main\AndroidManifest.xml:18:22-65
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\src\main\AndroidManifest.xml:19:5-76
	android:name
		ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\src\main\AndroidManifest.xml:19:22-74
uses-sdk
INJECTED from C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\src\main\AndroidManifest.xml
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\src\main\AndroidManifest.xml
