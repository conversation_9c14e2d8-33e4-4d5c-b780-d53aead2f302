1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="io.ionic.starter"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="23"
9        android:targetSdkVersion="35" />
10
11    <!-- Permissions -->
12
13    <uses-permission android:name="android.permission.INTERNET" />
13-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:40:5-67
13-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:40:22-64
14    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
14-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:41:5-79
14-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:41:22-76
15    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
15-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:42:5-81
15-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:42:22-78
16    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
16-->[:capacitor-firebase-messaging] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor-firebase\messaging\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:7:5-77
16-->[:capacitor-firebase-messaging] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor-firebase\messaging\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:7:22-74
17    <uses-permission android:name="android.permission.VIBRATE" />
17-->[:capacitor-haptics] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\haptics\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:7:5-66
17-->[:capacitor-haptics] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\haptics\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:7:22-63
18    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
18-->[:capacitor-local-notifications] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:7:5-81
18-->[:capacitor-local-notifications] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:7:22-78
19    <uses-permission android:name="android.permission.WAKE_LOCK" />
19-->[:capacitor-local-notifications] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:5-68
19-->[:capacitor-local-notifications] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:22-65
20    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" /> <!-- Required by older versions of Google Play services to create IID tokens -->
20-->[:capacitor-network] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\network\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:7:5-79
20-->[:capacitor-network] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\network\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:7:22-76
21    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
21-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c04d43294058e70b8ad79d5184e7401\transformed\firebase-messaging-24.1.0\AndroidManifest.xml:26:5-82
21-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c04d43294058e70b8ad79d5184e7401\transformed\firebase-messaging-24.1.0\AndroidManifest.xml:26:22-79
22
23    <permission
23-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5f51ed623ec66baebfa6a053fe8a8b2a\transformed\core-1.15.0\AndroidManifest.xml:22:5-24:47
24        android:name="io.ionic.starter.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
24-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5f51ed623ec66baebfa6a053fe8a8b2a\transformed\core-1.15.0\AndroidManifest.xml:23:9-81
25        android:protectionLevel="signature" />
25-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5f51ed623ec66baebfa6a053fe8a8b2a\transformed\core-1.15.0\AndroidManifest.xml:24:9-44
26
27    <uses-permission android:name="io.ionic.starter.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
27-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5f51ed623ec66baebfa6a053fe8a8b2a\transformed\core-1.15.0\AndroidManifest.xml:26:5-97
27-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5f51ed623ec66baebfa6a053fe8a8b2a\transformed\core-1.15.0\AndroidManifest.xml:26:22-94
28
29    <application
29-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:4:5-36:19
30        android:allowBackup="true"
30-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:5:9-35
31        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
31-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5f51ed623ec66baebfa6a053fe8a8b2a\transformed\core-1.15.0\AndroidManifest.xml:28:18-86
32        android:extractNativeLibs="false"
33        android:icon="@mipmap/ic_launcher"
33-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:6:9-43
34        android:label="@string/app_name"
34-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:7:9-41
35        android:roundIcon="@mipmap/ic_launcher_round"
35-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:8:9-54
36        android:supportsRtl="true"
36-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:9:9-35
37        android:theme="@style/AppTheme"
37-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:10:9-40
38        android:usesCleartextTraffic="true" >
38-->[:capacitor-cordova-android-plugins] C:\Users\<USER>\CAPSTONE\mobile_ionic\android\capacitor-cordova-android-plugins\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:18-53
39        <activity
39-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:12:9-25:20
40            android:name="io.ionic.starter.MainActivity"
40-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:14:13-41
41            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|locale|smallestScreenSize|screenLayout|uiMode|navigation"
41-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:13:13-140
42            android:exported="true"
42-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:18:13-36
43            android:label="@string/title_activity_main"
43-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:15:13-56
44            android:launchMode="singleTask"
44-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:17:13-44
45            android:theme="@style/AppTheme.NoActionBarLaunch" >
45-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:16:13-62
46            <intent-filter>
46-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:20:13-23:29
47                <action android:name="android.intent.action.MAIN" />
47-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:21:17-69
47-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:21:25-66
48
49                <category android:name="android.intent.category.LAUNCHER" />
49-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:22:17-77
49-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:22:27-74
50            </intent-filter>
51        </activity>
52
53        <provider
54            android:name="androidx.core.content.FileProvider"
54-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:28:13-62
55            android:authorities="io.ionic.starter.fileprovider"
55-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:29:13-64
56            android:exported="false"
56-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:30:13-37
57            android:grantUriPermissions="true" >
57-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:31:13-47
58            <meta-data
58-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:32:13-34:64
59                android:name="android.support.FILE_PROVIDER_PATHS"
59-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:33:17-67
60                android:resource="@xml/file_paths" />
60-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:34:17-51
61        </provider>
62
63        <service
63-->[:capacitor-firebase-messaging] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor-firebase\messaging\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:9-16:19
64            android:name="io.capawesome.capacitorjs.plugins.firebase.messaging.MessagingService"
64-->[:capacitor-firebase-messaging] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor-firebase\messaging\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:13-97
65            android:exported="false" >
65-->[:capacitor-firebase-messaging] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor-firebase\messaging\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:12:13-37
66            <intent-filter>
66-->[:capacitor-firebase-messaging] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor-firebase\messaging\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:13-15:29
67                <action android:name="com.google.firebase.MESSAGING_EVENT" />
67-->[:capacitor-firebase-messaging] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor-firebase\messaging\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:17-78
67-->[:capacitor-firebase-messaging] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor-firebase\messaging\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:25-75
68            </intent-filter>
69        </service>
70
71        <receiver android:name="com.capacitorjs.plugins.localnotifications.TimedNotificationPublisher" />
71-->[:capacitor-local-notifications] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:12:9-106
71-->[:capacitor-local-notifications] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:12:19-103
72        <receiver android:name="com.capacitorjs.plugins.localnotifications.NotificationDismissReceiver" />
72-->[:capacitor-local-notifications] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:9-107
72-->[:capacitor-local-notifications] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:19-104
73        <receiver
73-->[:capacitor-local-notifications] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:9-23:20
74            android:name="com.capacitorjs.plugins.localnotifications.LocalNotificationRestoreReceiver"
74-->[:capacitor-local-notifications] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:13-103
75            android:directBootAware="true"
75-->[:capacitor-local-notifications] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:16:13-43
76            android:exported="false" >
76-->[:capacitor-local-notifications] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:17:13-37
77            <intent-filter>
77-->[:capacitor-local-notifications] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:18:13-22:29
78                <action android:name="android.intent.action.LOCKED_BOOT_COMPLETED" />
78-->[:capacitor-local-notifications] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:19:17-86
78-->[:capacitor-local-notifications] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:19:25-83
79                <action android:name="android.intent.action.BOOT_COMPLETED" />
79-->[:capacitor-local-notifications] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:20:17-79
79-->[:capacitor-local-notifications] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:20:25-76
80                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
80-->[:capacitor-local-notifications] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:21:17-82
80-->[:capacitor-local-notifications] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:21:25-79
81            </intent-filter>
82        </receiver>
83
84        <service android:name="de.appplant.cordova.plugin.background.ForegroundService" />
84-->[:capacitor-cordova-android-plugins] C:\Users\<USER>\CAPSTONE\mobile_ionic\android\capacitor-cordova-android-plugins\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:9-91
84-->[:capacitor-cordova-android-plugins] C:\Users\<USER>\CAPSTONE\mobile_ionic\android\capacitor-cordova-android-plugins\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:18-88
85
86        <receiver
86-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c04d43294058e70b8ad79d5184e7401\transformed\firebase-messaging-24.1.0\AndroidManifest.xml:29:9-40:20
87            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
87-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c04d43294058e70b8ad79d5184e7401\transformed\firebase-messaging-24.1.0\AndroidManifest.xml:30:13-78
88            android:exported="true"
88-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c04d43294058e70b8ad79d5184e7401\transformed\firebase-messaging-24.1.0\AndroidManifest.xml:31:13-36
89            android:permission="com.google.android.c2dm.permission.SEND" >
89-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c04d43294058e70b8ad79d5184e7401\transformed\firebase-messaging-24.1.0\AndroidManifest.xml:32:13-73
90            <intent-filter>
90-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c04d43294058e70b8ad79d5184e7401\transformed\firebase-messaging-24.1.0\AndroidManifest.xml:33:13-35:29
91                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
91-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c04d43294058e70b8ad79d5184e7401\transformed\firebase-messaging-24.1.0\AndroidManifest.xml:34:17-81
91-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c04d43294058e70b8ad79d5184e7401\transformed\firebase-messaging-24.1.0\AndroidManifest.xml:34:25-78
92            </intent-filter>
93
94            <meta-data
94-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c04d43294058e70b8ad79d5184e7401\transformed\firebase-messaging-24.1.0\AndroidManifest.xml:37:13-39:40
95                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
95-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c04d43294058e70b8ad79d5184e7401\transformed\firebase-messaging-24.1.0\AndroidManifest.xml:38:17-92
96                android:value="true" />
96-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c04d43294058e70b8ad79d5184e7401\transformed\firebase-messaging-24.1.0\AndroidManifest.xml:39:17-37
97        </receiver>
98        <!--
99             FirebaseMessagingService performs security checks at runtime,
100             but set to not exported to explicitly avoid allowing another app to call it.
101        -->
102        <service
102-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c04d43294058e70b8ad79d5184e7401\transformed\firebase-messaging-24.1.0\AndroidManifest.xml:46:9-53:19
103            android:name="com.google.firebase.messaging.FirebaseMessagingService"
103-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c04d43294058e70b8ad79d5184e7401\transformed\firebase-messaging-24.1.0\AndroidManifest.xml:47:13-82
104            android:directBootAware="true"
104-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c04d43294058e70b8ad79d5184e7401\transformed\firebase-messaging-24.1.0\AndroidManifest.xml:48:13-43
105            android:exported="false" >
105-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c04d43294058e70b8ad79d5184e7401\transformed\firebase-messaging-24.1.0\AndroidManifest.xml:49:13-37
106            <intent-filter android:priority="-500" >
106-->[:capacitor-firebase-messaging] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor-firebase\messaging\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:13-15:29
107                <action android:name="com.google.firebase.MESSAGING_EVENT" />
107-->[:capacitor-firebase-messaging] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor-firebase\messaging\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:17-78
107-->[:capacitor-firebase-messaging] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor-firebase\messaging\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:25-75
108            </intent-filter>
109        </service>
110        <service
110-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c04d43294058e70b8ad79d5184e7401\transformed\firebase-messaging-24.1.0\AndroidManifest.xml:54:9-63:19
111            android:name="com.google.firebase.components.ComponentDiscoveryService"
111-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c04d43294058e70b8ad79d5184e7401\transformed\firebase-messaging-24.1.0\AndroidManifest.xml:55:13-84
112            android:directBootAware="true"
112-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f546ee03c2308c684eeebc8faf0e1407\transformed\firebase-common-21.0.0\AndroidManifest.xml:32:13-43
113            android:exported="false" >
113-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c04d43294058e70b8ad79d5184e7401\transformed\firebase-messaging-24.1.0\AndroidManifest.xml:56:13-37
114            <meta-data
114-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c04d43294058e70b8ad79d5184e7401\transformed\firebase-messaging-24.1.0\AndroidManifest.xml:57:13-59:85
115                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
115-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c04d43294058e70b8ad79d5184e7401\transformed\firebase-messaging-24.1.0\AndroidManifest.xml:58:17-122
116                android:value="com.google.firebase.components.ComponentRegistrar" />
116-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c04d43294058e70b8ad79d5184e7401\transformed\firebase-messaging-24.1.0\AndroidManifest.xml:59:17-82
117            <meta-data
117-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c04d43294058e70b8ad79d5184e7401\transformed\firebase-messaging-24.1.0\AndroidManifest.xml:60:13-62:85
118                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
118-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c04d43294058e70b8ad79d5184e7401\transformed\firebase-messaging-24.1.0\AndroidManifest.xml:61:17-119
119                android:value="com.google.firebase.components.ComponentRegistrar" />
119-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c04d43294058e70b8ad79d5184e7401\transformed\firebase-messaging-24.1.0\AndroidManifest.xml:62:17-82
120            <meta-data
120-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2d121929483b8667b8bd7f522bf5e661\transformed\firebase-installations-17.2.0\AndroidManifest.xml:15:13-17:85
121                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
121-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2d121929483b8667b8bd7f522bf5e661\transformed\firebase-installations-17.2.0\AndroidManifest.xml:16:17-130
122                android:value="com.google.firebase.components.ComponentRegistrar" />
122-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2d121929483b8667b8bd7f522bf5e661\transformed\firebase-installations-17.2.0\AndroidManifest.xml:17:17-82
123            <meta-data
123-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2d121929483b8667b8bd7f522bf5e661\transformed\firebase-installations-17.2.0\AndroidManifest.xml:18:13-20:85
124                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
124-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2d121929483b8667b8bd7f522bf5e661\transformed\firebase-installations-17.2.0\AndroidManifest.xml:19:17-127
125                android:value="com.google.firebase.components.ComponentRegistrar" />
125-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2d121929483b8667b8bd7f522bf5e661\transformed\firebase-installations-17.2.0\AndroidManifest.xml:20:17-82
126            <meta-data
126-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\324fc306ed84dc357040da54cc5f1fbc\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
127                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
127-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\324fc306ed84dc357040da54cc5f1fbc\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
128                android:value="com.google.firebase.components.ComponentRegistrar" />
128-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\324fc306ed84dc357040da54cc5f1fbc\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
129            <meta-data
129-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f546ee03c2308c684eeebc8faf0e1407\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
130                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
130-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f546ee03c2308c684eeebc8faf0e1407\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
131                android:value="com.google.firebase.components.ComponentRegistrar" />
131-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f546ee03c2308c684eeebc8faf0e1407\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
132            <meta-data
132-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d59858b3709795c3a4e2c9928bb49778\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:25:13-27:85
133                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
133-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d59858b3709795c3a4e2c9928bb49778\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:26:17-115
134                android:value="com.google.firebase.components.ComponentRegistrar" />
134-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d59858b3709795c3a4e2c9928bb49778\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:27:17-82
135        </service>
136
137        <activity
137-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3897ee7a3a7e64eb47ff9b7bb8256b24\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:9-173
138            android:name="com.google.android.gms.common.api.GoogleApiActivity"
138-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3897ee7a3a7e64eb47ff9b7bb8256b24\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:19-85
139            android:exported="false"
139-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3897ee7a3a7e64eb47ff9b7bb8256b24\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:146-170
140            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
140-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3897ee7a3a7e64eb47ff9b7bb8256b24\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:86-145
141
142        <provider
142-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f546ee03c2308c684eeebc8faf0e1407\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
143            android:name="com.google.firebase.provider.FirebaseInitProvider"
143-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f546ee03c2308c684eeebc8faf0e1407\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
144            android:authorities="io.ionic.starter.firebaseinitprovider"
144-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f546ee03c2308c684eeebc8faf0e1407\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
145            android:directBootAware="true"
145-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f546ee03c2308c684eeebc8faf0e1407\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
146            android:exported="false"
146-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f546ee03c2308c684eeebc8faf0e1407\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
147            android:initOrder="100" />
147-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f546ee03c2308c684eeebc8faf0e1407\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
148        <provider
148-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\455628e39230ce08b70f281b4ac99c3c\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
149            android:name="androidx.startup.InitializationProvider"
149-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\455628e39230ce08b70f281b4ac99c3c\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
150            android:authorities="io.ionic.starter.androidx-startup"
150-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\455628e39230ce08b70f281b4ac99c3c\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
151            android:exported="false" >
151-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\455628e39230ce08b70f281b4ac99c3c\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
152            <meta-data
152-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\455628e39230ce08b70f281b4ac99c3c\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
153                android:name="androidx.emoji2.text.EmojiCompatInitializer"
153-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\455628e39230ce08b70f281b4ac99c3c\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
154                android:value="androidx.startup" />
154-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\455628e39230ce08b70f281b4ac99c3c\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
155            <meta-data
155-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2086035d7e747a32c2be40e6ed7f404e\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
156                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
156-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2086035d7e747a32c2be40e6ed7f404e\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
157                android:value="androidx.startup" />
157-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2086035d7e747a32c2be40e6ed7f404e\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
158            <meta-data
158-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
159                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
159-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
160                android:value="androidx.startup" />
160-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
161        </provider>
162
163        <meta-data
163-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\697a983ff8b6be23efe7df3e3bbc5a94\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
164            android:name="com.google.android.gms.version"
164-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\697a983ff8b6be23efe7df3e3bbc5a94\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
165            android:value="@integer/google_play_services_version" />
165-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\697a983ff8b6be23efe7df3e3bbc5a94\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
166
167        <receiver
167-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
168            android:name="androidx.profileinstaller.ProfileInstallReceiver"
168-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
169            android:directBootAware="false"
169-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
170            android:enabled="true"
170-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
171            android:exported="true"
171-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
172            android:permission="android.permission.DUMP" >
172-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
173            <intent-filter>
173-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
174                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
174-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
174-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
175            </intent-filter>
176            <intent-filter>
176-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
177                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
177-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
177-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
178            </intent-filter>
179            <intent-filter>
179-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
180                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
180-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
180-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
181            </intent-filter>
182            <intent-filter>
182-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
183                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
183-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
183-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
184            </intent-filter>
185        </receiver>
186
187        <service
187-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c53ce620ea6072f75d375d7efaf4f97b\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
188            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
188-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c53ce620ea6072f75d375d7efaf4f97b\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
189            android:exported="false" >
189-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c53ce620ea6072f75d375d7efaf4f97b\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
190            <meta-data
190-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c53ce620ea6072f75d375d7efaf4f97b\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
191                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
191-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c53ce620ea6072f75d375d7efaf4f97b\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
192                android:value="cct" />
192-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c53ce620ea6072f75d375d7efaf4f97b\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
193        </service>
194        <service
194-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e166bb1d6f54168e00acc5493c1d998b\transformed\transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
195            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
195-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e166bb1d6f54168e00acc5493c1d998b\transformed\transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
196            android:exported="false"
196-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e166bb1d6f54168e00acc5493c1d998b\transformed\transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
197            android:permission="android.permission.BIND_JOB_SERVICE" >
197-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e166bb1d6f54168e00acc5493c1d998b\transformed\transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
198        </service>
199
200        <receiver
200-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e166bb1d6f54168e00acc5493c1d998b\transformed\transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
201            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
201-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e166bb1d6f54168e00acc5493c1d998b\transformed\transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
202            android:exported="false" />
202-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e166bb1d6f54168e00acc5493c1d998b\transformed\transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
203    </application>
204
205</manifest>
