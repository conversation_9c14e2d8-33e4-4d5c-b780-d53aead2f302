1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="io.capawesome.capacitorjs.plugins.firebase.messaging" >
4
5    <uses-sdk android:minSdkVersion="23" />
6
7    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
7-->C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor-firebase\messaging\android\src\main\AndroidManifest.xml:9:5-76
7-->C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor-firebase\messaging\android\src\main\AndroidManifest.xml:9:22-74
8
9    <application>
9-->C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor-firebase\messaging\android\src\main\AndroidManifest.xml:2:5-8:19
10        <service
10-->C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor-firebase\messaging\android\src\main\AndroidManifest.xml:3:9-7:19
11            android:name="io.capawesome.capacitorjs.plugins.firebase.messaging.MessagingService"
11-->C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor-firebase\messaging\android\src\main\AndroidManifest.xml:3:18-102
12            android:exported="false" >
12-->C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor-firebase\messaging\android\src\main\AndroidManifest.xml:3:103-127
13            <intent-filter>
13-->C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor-firebase\messaging\android\src\main\AndroidManifest.xml:4:13-6:29
14                <action android:name="com.google.firebase.MESSAGING_EVENT" />
14-->C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor-firebase\messaging\android\src\main\AndroidManifest.xml:5:17-78
14-->C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor-firebase\messaging\android\src\main\AndroidManifest.xml:5:25-75
15            </intent-filter>
16        </service>
17    </application>
18
19</manifest>
