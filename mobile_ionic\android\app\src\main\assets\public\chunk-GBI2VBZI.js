import{a as G}from"./chunk-G3WDI65I.js";import{a as Y}from"./chunk-4PHGPBSW.js";import"./chunk-UFD7UJFV.js";import{a as V,b as H}from"./chunk-CCKNKBVQ.js";import{a as m}from"./chunk-FULEFYAM.js";import"./chunk-AGHLTJ5J.js";import{$ as k,A as P,Cb as $,Da as C,Db as q,F as i,G as s,H as f,J as d,Ja as F,M as g,Na as N,Q as v,Qa as j,R as b,S as y,Wa as z,ab as R,bb as D,cb as B,ea as T,fb as U,g as u,h as M,ha as x,ia as O,ja as S,ka as I,la as L,m as _,ma as E,o as p,oa as A,y as w,z as c,zb as W}from"./chunk-QCXYQNJC.js";import"./chunk-6WVAEWPV.js";import"./chunk-HYNAH5QB.js";import"./chunk-5AIHQZWU.js";import"./chunk-4PQ5B4D2.js";import"./chunk-HC6MZPB3.js";import"./chunk-SV2ZKNWA.js";import"./chunk-EPGIQT2W.js";import"./chunk-RS5W3JWO.js";import"./chunk-DY4KE6AI.js";import"./chunk-GIGBYVJT.js";import"./chunk-KGEDUKSE.js";import"./chunk-MCRJI3T3.js";import"./chunk-OBBPMR2I.js";import"./chunk-AMQPVFGX.js";import"./chunk-KKCAABTQ.js";import"./chunk-OFX7WKKZ.js";import"./chunk-F4H6ZFEG.js";import"./chunk-NMYJD6OP.js";import"./chunk-XXJXE6HG.js";import"./chunk-SV7S5NYR.js";import"./chunk-WTCPO44B.js";import"./chunk-QVY4QQUF.js";import"./chunk-2HRRFJKF.js";import"./chunk-UYQ7EZNZ.js";import"./chunk-BAKMWPBW.js";import"./chunk-7D6K5XYM.js";import"./chunk-OBXDPQ3V.js";import{g as o}from"./chunk-2R6CW7ES.js";var K=(()=>{class a{constructor(e,n,t){this.http=e,this.platform=n,this.toastCtrl=t}checkBackendConnectivity(){return o(this,null,function*(){try{console.log("Checking backend connectivity to:",m.apiUrl);let e=yield u(this.http.get(`${m.apiUrl.replace("/api","")}/up`).pipe(M(5e3)));return console.log("Backend connectivity check successful"),!0}catch(e){return console.error("Backend connectivity check failed:",e),console.error("Error details:",{status:e.status,statusText:e.statusText,url:e.url,message:e.message}),e.status===0||e.name==="TimeoutError"?(console.log("Backend server not reachable - offline mode available"),!1):(console.log("Backend is reachable but returned an error:",e.status),!0)}})}checkMapboxConnectivity(){return o(this,null,function*(){try{console.log("Checking Mapbox connectivity...");let e=`https://api.mapbox.com/directions/v5/mapbox/walking/121.7740,12.8797;121.7750,12.8807?access_token=${m.mapboxAccessToken}&overview=simplified`,n=yield u(this.http.get(e));return console.log("Mapbox connectivity check successful"),!0}catch(e){return console.error("Mapbox connectivity check failed:",e),e.status===0?this.showConnectivityError("Mapbox","Cannot connect to Mapbox API. Please check your internet connection."):e.status===401?this.showConnectivityError("Mapbox","Invalid Mapbox access token. Please check your token."):e.status===403?this.showConnectivityError("Mapbox","Mapbox access denied. Please check your token permissions."):e.status===429&&this.showConnectivityError("Mapbox","Too many requests to Mapbox. Please wait and try again."),!1}})}checkNetworkConnectivity(){return o(this,null,function*(){console.log("Starting comprehensive network connectivity check...");let e={backend:!1,routing:!1};return e.backend=yield this.checkBackendConnectivity(),e.routing=yield this.checkMapboxConnectivity(),console.log("Network connectivity check results:",e),e})}showConnectivityError(e,n){return o(this,null,function*(){yield(yield this.toastCtrl.create({header:`${e} Connection Error`,message:n,duration:5e3,color:"danger",buttons:[{text:"Dismiss",role:"cancel"}]})).present()})}pingTest(){return o(this,null,function*(){try{let e=yield u(this.http.get("https://httpbin.org/get"));return!0}catch(e){return console.error("Ping test failed:",e),!1}})}getNetworkInfo(){return this.platform.is("capacitor")?{platform:"mobile",userAgent:navigator.userAgent}:{platform:"web",online:navigator.onLine,userAgent:navigator.userAgent}}static{this.\u0275fac=function(n){return new(n||a)(p(k),p(C),p($))}}static{this.\u0275prov=_({token:a,factory:a.\u0275fac,providedIn:"root"})}}return a})();var pe=(()=>{class a{goToRegister(){this.router.navigate(["/register"])}openNetworkDiagnostics(){this.router.navigate(["/network-diagnostics"])}openEnvironmentSwitcher(){this.router.navigate(["/environment-switcher"])}constructor(e,n,t,r,l,h,J,Q,X){this.router=e,this.authService=n,this.fcm=t,this.http=r,this.alertController=l,this.platform=h,this.fcmService=J,this.offlineStorage=Q,this.networkService=X,this.credentials={email:"",password:""},this.errorMessage="",this.fcmToken="",this.fcmTokenReady=!1}ngOnInit(){return o(this,null,function*(){console.log("\u{1F525} Login page initializing..."),yield this.initializeFCM()})}initializeFCM(){return o(this,null,function*(){try{console.log("\u{1F525} Initializing FCM for login..."),yield this.fcmService.initPush(),yield this.getFCMToken(),console.log("\u2705 FCM initialization complete, token ready:",!!this.fcmToken),this.fcmTokenReady=!0}catch(e){console.error("\u274C FCM initialization failed:",e),this.fcmTokenReady=!1}})}getFCMToken(){return o(this,null,function*(){try{if(!this.platform.is("cordova")&&!this.platform.is("capacitor")){console.log("Running in browser, using mock FCM token"),this.fcmToken="browser-mock-token-"+Math.random().toString(36).substring(2,15),console.log("Mock FCM Token:",this.fcmToken);return}console.log("Getting FCM token from service..."),this.fcmToken=yield this.fcmService.getToken(),console.log("\u2705 FCM Token obtained:",this.fcmToken.substring(0,20)+"...")}catch(e){console.error("\u274C Error getting FCM token from service:",e);try{console.log("Trying direct FCM plugin as fallback..."),this.fcmToken=yield this.fcm.getToken(),console.log("\u2705 FCM Token from direct plugin:",this.fcmToken.substring(0,20)+"...")}catch(n){console.error("\u274C All FCM token methods failed:",n),this.fcmToken=""}}})}registerTokenWithEndpoint(e,n,t,r){if(localStorage.getItem("fcm_token")===this.fcmToken){console.log("Token already registered, skipping registration"),t&&t();return}if(localStorage.getItem("fcm_token_registering")==="true"){console.log("Token registration already in progress, skipping"),t&&t();return}localStorage.setItem("fcm_token_registering","true"),this.http.post(e,n).subscribe({next:h=>{console.log(`FCM token registered with ${e}:`,h),localStorage.setItem("fcm_token",this.fcmToken),localStorage.removeItem("fcm_token_registering"),t&&t()},error:h=>{console.error(`Error registering token with ${e}:`,h),localStorage.removeItem("fcm_token_registering"),r&&r()}})}onLogin(){return o(this,null,function*(){if(!this.credentials.email||!this.credentials.password){yield this.presentAlert("Login Failed","Please enter both email and password.");return}console.log("\u{1F510} Login attempt started"),console.log("\u{1F4E7} Email:",this.credentials.email),console.log("\u{1F310} API URL:",m.apiUrl),console.log("\u{1F4F1} Platform:",this.platform.is("android")?"Android":this.platform.is("ios")?"iOS":"Browser"),console.log("\u{1F30D} Network status:",navigator.onLine?"Online":"Offline"),console.log("\uFFFD Offline mode:",this.offlineStorage.isOfflineMode()),console.log("\uFFFD\u{1F525} FCM Token ready:",this.fcmTokenReady,"Token:",this.fcmToken?this.fcmToken.substring(0,20)+"...":"None");let e=this.offlineStorage.isOfflineMode(),n=navigator.onLine;if(!n||e){if(console.log("\u{1F504} Attempting offline authentication..."),yield this.attemptOfflineLogin())return;if(!n){yield this.presentOfflineAlert();return}}if(n&&!e&&!this.fcmTokenReady&&!this.fcmToken){console.log("\u{1F525} FCM token not ready, attempting to get it now...");try{yield this.getFCMToken()}catch(t){console.warn("\u26A0\uFE0F Could not get FCM token, continuing without it:",t)}}if(n&&!e&&(console.log("\u{1F9EA} Testing API connectivity..."),!(yield this.networkService.checkBackendConnectivity()))){yield this.presentConnectionErrorAlert();return}this.authService.login(this.credentials).subscribe({next:t=>o(this,null,function*(){if(console.log("\u2705 Login successful:",t),yield this.presentSuccessAlert("Login Successful","Welcome, "+t.user.full_name),this.authService.setToken(t.token),this.fcmToken){console.log("Registering FCM token with backend:",this.fcmToken);let r={token:this.fcmToken,device_type:this.platform.is("ios")?"ios":"android",project_id:m.firebase.projectId};t.user&&t.user.id&&(r.user_id=t.user.id),console.log("Token registration payload:",r),console.log("API URL:",`${m.apiUrl}/device-token`),this.fcmService.registerTokenWithBackend(this.fcmToken,t.user.id),this.router.navigate(["/welcome"])}else console.warn("No FCM token available to register"),this.router.navigate(["/welcome"])}),error:t=>{console.error("\u274C Login error:",t),console.error("\u{1F4CA} Error details:",{status:t.status,statusText:t.statusText,message:t.message,url:t.url,error:t.error}),this.errorMessage=t.error?.message||"Login failed",t.status===0?this.presentAlert("Network Error",`Cannot connect to the server. Please check:
\u2022 Your internet connection
\u2022 If you're on the same WiFi network as the server
\u2022 If the backend server is running

Server URL: ${m.apiUrl}`):t.status===401?this.presentAlert("Login Failed","Invalid email or password. Please try again."):t.status===404?this.presentAlert("Server Error","Login endpoint not found. Please check server configuration."):t.status>=500?this.presentAlert("Server Error","The server encountered an error. Please try again later."):this.presentAlert("Login Error",`An error occurred during login (${t.status}).

Please check the console for more details or try again later.`)}})})}presentAlert(e,n){return o(this,null,function*(){yield(yield this.alertController.create({header:e,message:n,buttons:["OK"],cssClass:"login-alert"})).present()})}presentSuccessAlert(e,n){return o(this,null,function*(){yield(yield this.alertController.create({header:e,message:n,buttons:["OK"],cssClass:"login-success-alert"})).present()})}attemptOfflineLogin(){return o(this,null,function*(){try{let e=localStorage.getItem("offline_credentials");if(!e)return console.log("\u274C No offline credentials stored"),!1;let n=JSON.parse(e);return n.email===this.credentials.email&&n.password===this.credentials.password?(console.log("\u2705 Offline login successful"),this.authService.setToken("offline_token_"+Date.now()),yield this.presentSuccessAlert("Offline Login","Logged in using cached credentials"),this.router.navigate(["/welcome"]),!0):(console.log("\u274C Offline credentials do not match"),!1)}catch(e){return console.error("\u274C Error during offline login:",e),!1}})}presentOfflineAlert(){return o(this,null,function*(){yield(yield this.alertController.create({header:"No Internet Connection",message:"You are currently offline. Please check your internet connection and try again, or continue in offline mode if you have previously logged in.",buttons:[{text:"Retry",handler:()=>{this.onLogin()}},{text:"Continue Offline",handler:()=>{this.attemptOfflineLogin()}}],cssClass:"offline-alert"})).present()})}presentConnectionErrorAlert(){return o(this,null,function*(){yield(yield this.alertController.create({header:"Connection Error",message:`Cannot connect to the server at ${m.apiUrl}.

Please check:
\u2022 Your internet connection
\u2022 If the backend server is running
\u2022 If you're on the same network as the server`,buttons:[{text:"Retry",handler:()=>{this.onLogin()}},{text:"Network Diagnostics",handler:()=>{this.router.navigate(["/network-diagnostics"])}}],cssClass:"connection-error-alert"})).present()})}static{this.\u0275fac=function(n){return new(n||a)(c(T),c(Y),c(V),c(k),c(W),c(C),c(H),c(G),c(K))}}static{this.\u0275cmp=P({type:a,selectors:[["app-login"]],decls:36,vars:2,consts:[[1,"ion-padding"],[1,"login-container"],[1,"login-wrapper"],["src","assets/ALERTO.png","alt","App Logo",1,"login-logo"],[1,"login-title"],[1,"login-form",3,"ngSubmit"],["position","floating"],["type","email","name","email","required","",3,"ngModelChange","ngModel"],["type","password","name","password","required","",3,"ngModelChange","ngModel"],["expand","block","type","submit",1,"login-btn"],[1,"troubleshooting-section"],["expand","block","fill","outline","color","warning",3,"click"],["name","settings-outline","slot","start"],["expand","block","fill","clear","size","small",3,"click"],["name","bug-outline","slot","start"],["expand","block","fill","outline","color","secondary",3,"click"],[1,"login-link"],[3,"click"]],template:function(n,t){n&1&&(i(0,"ion-content",0)(1,"div",1)(2,"ion-card-content")(3,"div",2),f(4,"img",3),i(5,"h1",4),g(6,"Log In Here!"),s(),f(7,"p"),i(8,"form",5),d("ngSubmit",function(){return t.onLogin()}),i(9,"ion-item")(10,"ion-label",6),g(11,"Email:"),s(),i(12,"ion-input",7),y("ngModelChange",function(l){return b(t.credentials.email,l)||(t.credentials.email=l),l}),s()(),i(13,"ion-item")(14,"ion-label",6),g(15,"Password:"),s(),i(16,"ion-input",8),y("ngModelChange",function(l){return b(t.credentials.password,l)||(t.credentials.password=l),l}),s()(),f(17,"br")(18,"br"),i(19,"ion-button",9),g(20,"Log In"),s()(),i(21,"div",10)(22,"ion-button",11),d("click",function(){return t.openEnvironmentSwitcher()}),f(23,"ion-icon",12),g(24," Switch API Endpoint "),s(),i(25,"ion-button",13),d("click",function(){return t.openNetworkDiagnostics()}),f(26,"ion-icon",14),g(27," Network Diagnostics "),s()(),i(28,"ion-button",15),d("click",function(){return t.openNetworkDiagnostics()}),g(29," \u{1F527} Network Diagnostics "),s(),i(30,"div",16),g(31," Don't have an account? "),i(32,"a",17),d("click",function(){return t.goToRegister()}),i(33,"strong")(34,"u"),g(35,"Sign Up"),s()()()()()()()()),n&2&&(w(12),v("ngModel",t.credentials.email),w(4),v("ngModel",t.credentials.password))},dependencies:[q,N,j,z,R,D,B,U,F,A,L,x,O,E,I,S],styles:[".login-container[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;height:80vh}.login-wrapper[_ngcontent-%COMP%]{width:100%;max-width:420px;padding:32px 28px;margin:0 auto;display:flex;flex-direction:column;align-items:center}.login-logo[_ngcontent-%COMP%]{width:300px;height:300px}.login-title[_ngcontent-%COMP%]{font-size:2.2rem;font-weight:700}.login-desc[_ngcontent-%COMP%]{font-size:1.1rem;color:#888}.login-form[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{font-size:1.1rem;border-radius:16px}.login-btn[_ngcontent-%COMP%]{--border-radius: 25px;font-size:1.2rem;height:48px}.login-link[_ngcontent-%COMP%]{margin-top:20px;font-size:1.1rem}.login-link[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{font-size:1.1rem;font-weight:700}.troubleshooting-section[_ngcontent-%COMP%]{margin-top:24px;padding-top:16px;border-top:1px solid var(--ion-color-light)}.troubleshooting-section[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{margin-bottom:8px}.login-alert[_ngcontent-%COMP%]{--backdrop-opacity: .8}.login-alert[_ngcontent-%COMP%]   .alert-wrapper[_ngcontent-%COMP%]{border-radius:15px;box-shadow:0 4px 16px #0003}.login-alert[_ngcontent-%COMP%]   .alert-head[_ngcontent-%COMP%]{padding-bottom:10px}.login-alert[_ngcontent-%COMP%]   .alert-title[_ngcontent-%COMP%]{font-size:1.2rem;font-weight:600;color:#d9534f}.login-alert[_ngcontent-%COMP%]   .alert-message[_ngcontent-%COMP%]{font-size:1rem;color:#333}.login-alert[_ngcontent-%COMP%]   .alert-button[_ngcontent-%COMP%]{color:#3880ff;font-weight:500}.login-success-alert[_ngcontent-%COMP%]{--backdrop-opacity: .8}.login-success-alert[_ngcontent-%COMP%]   .alert-wrapper[_ngcontent-%COMP%]{border-radius:15px;box-shadow:0 4px 16px #0003}.login-success-alert[_ngcontent-%COMP%]   .alert-head[_ngcontent-%COMP%]{padding-bottom:10px}.login-success-alert[_ngcontent-%COMP%]   .alert-title[_ngcontent-%COMP%]{font-size:1.2rem;font-weight:600;color:#5cb85c}.login-success-alert[_ngcontent-%COMP%]   .alert-message[_ngcontent-%COMP%]{font-size:1rem;color:#333}.login-success-alert[_ngcontent-%COMP%]   .alert-button[_ngcontent-%COMP%]{color:#3880ff;font-weight:500}"]})}}return a})();export{pe as LoginPage};
