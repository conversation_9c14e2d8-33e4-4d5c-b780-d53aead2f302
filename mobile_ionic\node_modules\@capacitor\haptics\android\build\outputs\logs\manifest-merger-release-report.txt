-- Merging decision tree log ---
manifest
ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\haptics\android\src\main\AndroidManifest.xml:1:1-3:12
INJECTED from C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\haptics\android\src\main\AndroidManifest.xml:1:1-3:12
	package
		INJECTED from C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\haptics\android\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\haptics\android\src\main\AndroidManifest.xml:1:11-69
uses-permission#android.permission.VIBRATE
ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\haptics\android\src\main\AndroidManifest.xml:2:5-66
	android:name
		ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\haptics\android\src\main\AndroidManifest.xml:2:22-63
uses-sdk
INJECTED from C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\haptics\android\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\haptics\android\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\haptics\android\src\main\AndroidManifest.xml
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\haptics\android\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\haptics\android\src\main\AndroidManifest.xml
