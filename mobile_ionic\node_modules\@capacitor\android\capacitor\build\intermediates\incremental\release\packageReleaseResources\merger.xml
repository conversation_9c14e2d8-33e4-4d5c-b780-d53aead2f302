<?xml version="1.0" encoding="utf-8"?>
<merger version="3" xmlns:ns1="http://schemas.android.com/tools"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\android\capacitor\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\android\capacitor\src\main\res"><file name="bridge_layout_main" path="C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\android\capacitor\src\main\res\layout\bridge_layout_main.xml" qualifiers="" type="layout"/><file name="capacitor_bridge_layout_main" path="C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\android\capacitor\src\main\res\layout\capacitor_bridge_layout_main.xml" qualifiers="" type="layout"/><file name="no_webview" path="C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\android\capacitor\src\main\res\layout\no_webview.xml" qualifiers="" type="layout"/><file path="C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\android\capacitor\src\main\res\values\colors.xml" qualifiers=""><color name="colorPrimary" ns1:ignore="UnusedResources">#3F51B5</color><color name="colorPrimaryDark" ns1:ignore="UnusedResources">#303F9F</color><color name="colorAccent" ns1:ignore="UnusedResources">#FF4081</color></file><file path="C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\android\capacitor\src\main\res\values\strings.xml" qualifiers=""><string name="no_webview_text">This app requires a WebView to work</string></file><file path="C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\android\capacitor\src\main\res\values\styles.xml" qualifiers=""><style name="AppTheme.NoActionBar" parent="Theme.AppCompat.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style></file></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="release$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\android\capacitor\src\release\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="release" generated-set="release$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\android\capacitor\src\release\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\android\capacitor\build\generated\res\resValues\release"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\android\capacitor\build\generated\res\resValues\release"/></dataSet><mergedItems/></merger>